<template>
    <div class="main-content">
        <div class="content-wrapper">
            <div class="welcome-index">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" class="logo-main" />
                    </div>
                    <h1 class="welcome-title">{{ welcomeConfig.title }}</h1>
                    <p class="welcome-desc">{{ welcomeConfig.description }}</p>
                </div>

                <!-- 功能卡片区域 -->
                <div class="feature-cards">
                    <el-card
                        class="feature-card"
                        :style="{ '--card-bg': `url(${card.bg})` }"
                        v-for="(card, index) in featureCards"
                        :key="index"
                        shadow="never"
                        :body-style="{ padding: 0, height: '100%' }"
                        @click.native="handleCardClick(card)"
                    >
                        <div class="card-content">
                            <div class="card-icon">
                                <img :src="card.icon" :alt="card.title" />
                            </div>
                            <h3 class="card-title">{{ card.title }}</h3>
                            <p class="card-desc text-ellipsis">{{ card.description }}</p>
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-section">
                <div class="input-container">
                    <textarea
                        id="chat-input"
                        class="chat-input custom-scrollbar"
                        v-model="inputText"
                        :placeholder="inputConfig.placeholder"
                        resize="none"
                        rows="2"
                        @keydown.native="handleKeydown"
                    ></textarea>
                    <div class="input-actions">
                        <el-select
                            v-model="selectedModel"
                            size="small"
                            class="model-select"
                            popper-class="dataUsageAssistant-theme model-select-popper"
                        >
                            <el-option
                                v-for="model in modelOptions"
                                :key="model.value"
                                :label="model.label"
                                :value="model.value"
                            >
                                <div class="model-option">
                                    <div class="model-option-text">
                                        <div class="model-name">{{ model.label }}</div>
                                        <div class="model-desc">{{ model.desc }}</div>
                                    </div>
                                    <i
                                        class="el-icon-check model-selected"
                                        v-if="selectedModel === model.value"
                                    ></i>
                                </div>
                            </el-option>
                        </el-select>
                        <div class="right-actions">
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="语音输入"
                                placement="top"
                                popper-class="mic-popper"
                            >
                                <div class="mic-btn">
                                    <i class="el-icon-microphone"></i>
                                </div>
                            </el-tooltip>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                content="请输入你的问题"
                                placement="top"
                                popper-class="mic-popper"
                                :disabled="!!inputText.trim()"
                            >
                                <div
                                    class="send-btn"
                                    :class="{ 'is-disabled': !inputText.trim() }"
                                    @click="sendMessage"
                                >
                                    <i class="el-icon-top"></i>
                                </div>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MainContent',
    props: {
        welcomeConfig: {
            type: Object,
            default: () => ({
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            })
        },
        inputConfig: {
            type: Object,
            default: () => ({
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            })
        },
        featureCards: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => [
                { label: 'DeepSeek V3', value: 'deepseek-v3', desc: '适合大部分任务' },
                { label: 'GPT-4', value: 'gpt-4', desc: '适合多种应用场景' },
                { label: 'Claude-3', value: 'claude-3', desc: '适合多种应用场景' }
            ]
        }
    },
    data() {
        return {
            inputText: '',
            selectedModel: 'deepseek-v3'
        };
    },
    methods: {
        // 处理功能卡片点击
        handleCardClick(card) {
            this.$emit('card-click', card);

            // 根据卡片类型设置预设问题
            const presetQuestions = {
                'location-capability': '请为我推荐位置能力使用方案',
                'asset-subscription': '我想了解位置资产订阅相关问题',
                'business-requirements': '请帮我分析位置业务需求数据'
            };

            if (presetQuestions[card.id]) {
                this.inputText = presetQuestions[card.id];
            }
        },

        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim()) {
                const message = {
                    text: this.inputText.trim(),
                    model: this.selectedModel,
                    timestamp: new Date().toISOString()
                };

                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        }
    }
};
</script>

<style scoped lang="less">
// 主内容区域样式
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;

        .welcome-index {
            min-height: 0;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            gap: 3.75rem;

            .welcome-section {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;

                .welcome-icon {
                    width: 6.25rem;
                    height: 6.25rem;
                    margin-bottom: 1.875rem;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .welcome-title {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 500;
                    font-size: 2rem;
                    color: #001024;
                    line-height: 2.5rem;
                    margin: 0 0 1.5rem 0;
                }

                .welcome-desc {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #666666;
                    line-height: 1.5rem;
                    margin-bottom: 0;
                }
            }

            .feature-cards {
                display: flex;
                gap: 1.5rem;

                .feature-card {
                    --card-bg: url('~@/img/main/card-bg-1.png');
                    width: 19rem;
                    height: 15.4375rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background-color: transparent;
                    background-image: var(--card-bg);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    border: none;
                    &:hover {
                        transform: translateY(-0.125rem);
                    }

                    .card-content {
                        width: 100%;
                        height: 100%;
                        padding: 1.5rem;

                        .card-icon {
                            margin-bottom: 1.125rem;

                            img {
                                width: 3rem;
                                height: 3rem;
                            }
                        }

                        .card-title {
                            font-weight: 500;
                            font-size: 1.25rem;
                            color: #222222;
                            line-height: 1.75rem;
                            margin: 0 0 0.75rem 0;
                        }

                        .card-desc {
                            --line-clamp: 4;
                            font-weight: 400;
                            font-size: 0.875rem;
                            color: #666666;
                            line-height: 1.25rem;
                            margin: 0;
                        }
                    }
                }
            }
        }

        .input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;

            .input-container {
                width: 60rem;
                height: 8.75rem;
                display: flex;
                flex-direction: column;
                background: rgba(255, 255, 255, 0.92);
                border-radius: 0.75rem;
                border: 0.0625rem solid #1765ff;
                padding: 0.75rem;
                gap: 0.75rem;

                .chat-input {
                    min-height: 0;
                    flex: 1;
                    resize: none;
                    border: none;
                    outline: none;
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #222222;
                    line-height: 1.375rem;
                    &::placeholder {
                        font-family: HONORSansCN, HONORSansCN;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #999999;
                    }
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .model-select {
                        width: 9.125rem;
                        height: 32px;
                        /deep/ .el-input__inner {
                            background: transparent;
                            border-radius: 0.5rem;
                            border: 0.0625rem solid #e0e0e0;
                            font-weight: 500;
                            font-size: 0.875rem;
                            color: #222222;
                        }
                    }
                    .right-actions {
                        margin-left: auto;
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                        .mic-btn {
                            width: 2rem;
                            height: 2rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: transparent;
                            border-radius: 0.375rem;
                            cursor: pointer;

                            .el-icon-microphone {
                                font-size: 1.25rem;
                                color: #0c1827;
                            }
                            &:hover {
                                background: rgba(2, 101, 254, 0.1);
                            }
                        }
                        .send-btn {
                            width: 2rem;
                            height: 2rem;
                            border-radius: 50%;
                            padding: 0;
                            background: #87b3fe;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            .el-icon-top {
                                color: #fff;
                                font-weight: 500;
                                font-size: 1rem;
                            }

                            &.is-disabled {
                                opacity: 0.5;
                                cursor: not-allowed;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
.model-select-popper {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow: 0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    .el-select-dropdown__item {
        height: fit-content;
        line-height: normal;
        padding: 0;
        &.hover {
            background: transparent;
        }
        &:hover {
            background: #f2f2f2;
        }
        &.selected {
            background: #f2f2f2;
        }
    }
    .el-select-dropdown__list {
        padding: 0;
    }
    .model-option {
        width: 10.75rem;
        height: 3.5rem;
        border-radius: 0.5rem;
        display: grid;
        grid-template-columns: 1fr 1.25rem;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem;
        .model-option-text {
            display: flex;
            flex-direction: column;
            height: fit-content;
            flex: 1;
            .model-name {
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                line-height: 1.25rem;
            }
            .model-desc {
                font-weight: 400;
                font-size: 0.75rem;
                color: #666666;
                line-height: 1.125rem;
            }
        }
        .model-selected {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1rem;
            color: #222222;
        }
    }
}
.mic-popper {
    &.el-tooltip__popper[x-placement^='top'] .popper__arrow {
        bottom: -5px;
    }
}
</style>
