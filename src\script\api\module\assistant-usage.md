# AgentServ API 使用说明

## 概述

本文档说明如何在项目中使用 AgentServ API 接口。API 接口已更新为使用 `aiConfigPost` 和 `aiConfigGet` 方法。

## API 配置

API 接口已配置在 `src/script/api/module/assistant.js` 文件中，使用新的传参方式：

### 基本使用方式

```javascript
import Assistant from '@/script/api/module/assistant.js';

// 聊天接口调用示例
Assistant.defaultChat({
    model: "deepseek-chat",
    messages: [
        {
            role: "user",
            content: "你好，请介绍一下位置大数据"
        }
    ],
    max_tokens: 2048,
    temperature: 0.7
}).then((res) => {
    console.log('AI回复:', res);
}).catch((error) => {
    console.error('请求失败:', error);
});
```

### 聊天接口

```javascript
// 1. 默认聊天接口（DeepSeek）
Assistant.defaultChat(params);

// 2. DeepSeek 服务
Assistant.deepseekChat(params);

// 3. Dify 服务
Assistant.difyChat({
    model: "dify-chatflow",
    messages: [...],
    parameters: {
        industry: "位置大数据",
        analysis_type: "market_trend",
        user_id: "analyst_001"
    }
});

// 4. OpenAI/vLLM 服务
Assistant.openaiChat({
    model: "qwen3-30b-a3b",
    messages: [...],
    max_tokens: 2048,
    temperature: 0.7
});

// 5. 根据服务类型发送消息
Assistant.chatByService('deepseek', params);
```

### 服务状态查询

```javascript
// 查看所有可用服务
Assistant.getServicesStatus().then((res) => {
    console.log('可用服务:', res);
});

// 健康检查
Assistant.healthCheck().then((res) => {
    console.log('服务状态:', res);
});

// 检查指定服务健康状态
Assistant.checkServiceHealth('deepseek').then((res) => {
    console.log('DeepSeek服务状态:', res);
});
```

### 模型列表

```javascript
// 获取默认服务模型列表
Assistant.getModels().then((res) => {
    console.log('模型列表:', res);
});

// 获取指定服务的模型列表
Assistant.getServiceModels('deepseek').then((res) => {
    console.log('DeepSeek模型列表:', res);
});
```

## 新的传参方式

所有 API 方法现在使用对象传参方式：

### POST 请求（聊天接口）
```javascript
aiConfigPost({
    url: '/v1/chat/completions',
    params: {
        model: "deepseek-chat",
        messages: [...],
        max_tokens: 2048,
        temperature: 0.7
    },
    gateWay: '/data-usage-assistant'
});
```

### GET 请求（状态查询、模型列表）
```javascript
aiConfigGet({
    url: '/v1/health',
    params: {},
    gateWay: '/data-usage-assistant'
});
```

## 响应处理

根据 `ai-service-config.js` 的实现，API 响应会经过以下处理：

### 成功响应
```javascript
// 当 result.success == '0' 时，返回 result.result
Assistant.defaultChat(params).then((res) => {
    // res 是 result.result 的内容
    console.log('成功响应:', res);
});
```

### 错误响应
```javascript
Assistant.defaultChat(params).catch((error) => {
    // error 包含以下结构：
    // {
    //     success: result.success,
    //     errorMessage: result.errorInfo.message,
    //     result: result
    // }
    console.error('错误信息:', error.errorMessage);
});
```

## 在组件中的使用

### HomePage.vue 中的集成示例

```javascript
// 处理发送消息
async handleSendMessage(message) {
    try {
        // 构建聊天历史
        const messages = this.chatMessages
            .filter(msg => msg.type === 'user' || msg.type === 'assistant')
            .map(msg => ({
                role: msg.type === 'user' ? 'user' : 'assistant',
                content: msg.text
            }));

        // API 参数
        const apiParams = {
            model: this.getModelName(message.model),
            messages: messages,
            max_tokens: 2048,
            temperature: 0.7
        };

        // 根据模型选择服务
        const service = this.getServiceByModel(message.model);
        let response;
        
        if (service === 'dify') {
            apiParams.parameters = {
                industry: '位置大数据',
                analysis_type: 'general',
                user_id: 'user_001'
            };
            response = await Assistant.difyChat(apiParams);
        } else if (service === 'openai') {
            response = await Assistant.openaiChat(apiParams);
        } else {
            response = await Assistant.defaultChat(apiParams);
        }

        // 处理响应
        if (response && response.choices && response.choices.length > 0) {
            const aiResponse = {
                text: response.choices[0].message.content,
                type: 'assistant',
                timestamp: new Date().toISOString(),
                model: message.model
            };
            this.chatMessages.push(aiResponse);
        }
    } catch (error) {
        console.error('API 调用失败:', error);
        // 处理错误...
    }
}
```

## 注意事项

1. **传参方式变更**: 所有方法现在使用对象传参 `{ url, params, gateWay }`
2. **GET/POST 区分**: 聊天接口使用 `aiConfigPost`，查询接口使用 `aiConfigGet`
3. **响应格式**: 成功时返回 `result.result`，失败时抛出包含错误信息的对象
4. **Token 认证**: 自动从 localStorage 获取 token 并添加到请求头
5. **网关配置**: 统一使用 `/data-usage-assistant` 作为网关前缀

## 错误处理

```javascript
try {
    const response = await Assistant.defaultChat(params);
    // 处理成功响应
} catch (error) {
    console.error('API 调用失败:', error);
    
    // 显示错误消息给用户
    const errorResponse = {
        text: `抱歉，服务暂时不可用。错误信息：${error.errorMessage || '未知错误'}`,
        type: 'assistant',
        timestamp: new Date().toISOString(),
        isError: true
    };
    this.chatMessages.push(errorResponse);
}
```
