# 组件重构说明

## 概述

本次重构将原来的 `HomePage.vue` 单一组件拆分为四个独立的 Vue 组件，并创建了一个通用的消息输入组件来消除重复代码，提高代码的可维护性和复用性。

## 组件结构

### 1. Sidebar.vue - 侧边栏组件
**位置**: `src/script/components/Sidebar.vue`

**功能**:
- 侧边栏展开/收起功能
- 搜索功能（带自动完成）
- 菜单列表显示
- 新对话按钮

**Props**:
- `menuGroups`: Array - 菜单组数据
- `activeMenuItem`: String/Number - 当前激活的菜单项ID

**Events**:
- `collapse-change`: 侧边栏收起状态变化
- `new-chat`: 新对话按钮点击
- `search`: 搜索输入
- `search-select`: 搜索项选择
- `menu-item-click`: 菜单项点击

### 2. MainContent.vue - 主内容区域组件
**位置**: `src/script/components/MainContent.vue`

**功能**:
- 欢迎页面显示
- 功能卡片展示
- 集成消息输入组件

**Props**:
- `welcomeConfig`: Object - 欢迎页面配置
- `inputConfig`: Object - 输入区域配置
- `featureCards`: Array - 功能卡片数据
- `modelOptions`: Array - 模型选项

**Events**:
- `card-click`: 功能卡片点击
- `send-message`: 发送消息

### 3. ChatPage.vue - 聊天页面组件
**位置**: `src/script/components/ChatPage.vue`

**功能**:
- 聊天消息显示
- 集成消息输入组件
- 空状态提示

**Props**:
- `messages`: Array - 聊天消息列表
- `modelOptions`: Array - 模型选项
- `placeholder`: String - 输入框占位符

**Events**:
- `send-message`: 发送消息

### 4. MessageInput.vue - 消息输入组件（新增）
**位置**: `src/script/components/MessageInput.vue`

**功能**:
- 统一的消息输入界面
- 支持多种主题样式
- 模型选择功能
- 语音输入按钮（可选）
- **键盘绑定**: Enter发送消息，Shift+Enter换行

**Props**:
- `placeholder`: String - 输入框占位符
- `inputId`: String - 输入框ID
- `modelOptions`: Array - 模型选项
- `defaultModel`: String - 默认模型
- `showMicButton`: Boolean - 是否显示语音按钮
- `showModelDetails`: Boolean - 是否显示模型详细信息
- `showScrollbar`: Boolean - 是否显示滚动条
- `theme`: String - 主题样式 ('home' | 'chat')
- `popperClass`: String - 下拉框样式类
- `sendButtonTooltip`: String - 发送按钮提示文本

**Events**:
- `send-message`: 发送消息
- `mic-click`: 语音输入点击

**方法**:
- `setInputText(text)`: 设置输入文本
- `clearInput()`: 清空输入框
- `focus()`: 聚焦输入框

## 键盘绑定

所有输入组件现在都支持统一的键盘绑定：
- **Enter键**: 发送消息
- **Shift+Enter**: 换行

## 使用方式

### MessageInput 组件的使用示例

```vue
<!-- 主页样式 -->
<MessageInput
    theme="home"
    :placeholder="inputConfig.placeholder"
    :model-options="modelOptions"
    :show-mic-button="true"
    :show-model-details="true"
    @send-message="handleSendMessage"
    @mic-click="handleMicClick"
/>

<!-- 聊天页样式 -->
<MessageInput
    theme="chat"
    :placeholder="placeholder"
    :model-options="modelOptions"
    @send-message="handleSendMessage"
/>
```

## 重构优势

1. **消除重复代码**: 创建了统一的 MessageInput 组件
2. **代码分离**: 每个组件职责单一，便于维护
3. **复用性**: 组件可以在其他页面中复用
4. **可测试性**: 每个组件可以独立测试
5. **可扩展性**: 便于后续功能扩展
6. **一致性**: 统一的键盘绑定和交互体验

## 文件结构

```
src/script/
├── components/
│   ├── Sidebar.vue          # 侧边栏组件
│   ├── MainContent.vue      # 主内容区域组件
│   ├── ChatPage.vue         # 聊天页面组件
│   ├── MessageInput.vue     # 消息输入组件（新增）
│   └── README.md           # 使用说明文档
└── plugin2x/main/components/
    └── HomePage.vue         # 重构后的主页组件
```

## 注意事项

1. 所有原有的样式都已正确迁移到对应组件中
2. 组件间的数据传递通过 props 和 events 实现
3. 保持了原有的功能逻辑不变
4. MessageInput 组件支持两种主题样式
5. 键盘绑定已统一为 Enter发送，Shift+Enter换行

## 后续扩展建议

1. 可以为 MessageInput 组件添加更多主题样式
2. 可以考虑将搜索功能进一步抽象为独立组件
3. 可以为各组件添加更多的配置选项以提高灵活性
4. 可以添加单元测试来确保组件的稳定性
