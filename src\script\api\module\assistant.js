import aiConfigPost from '../ai-service-config';

// AgentServ API 服务地址配置
const gateWay = '/data-usage-assistant';

export default {
    // ==================== 聊天接口 ====================

    // 默认聊天接口（使用DeepSeek）
    defaultChat(params) {
        return aiConfigPost('/v1/chat/completions', params, gateWay);
    },

    // DeepSeek 聊天接口
    deepseekChat(params) {
        return aiConfigPost('/v1/services/deepseek/chat/completions', params, gateWay);
    },

    // Dify 聊天接口（标准路径）
    difyChat(params) {
        return aiConfigPost('/v1/services/dify/chat/completions', params, gateWay);
    },

    // Dify 聊天接口（兼容路径）
    difyCompatChat(params) {
        return aiConfigPost('/v1/dify/chat/completions', params, gateWay);
    },

    // OpenAI/vLLM 聊天接口
    openaiChat(params) {
        return aiConfigPost('/v1/services/openai/chat/completions', params, gateWay);
    },

    // ==================== 服务状态查询 ====================

    // 查看所有可用服务
    getServicesStatus() {
        return aiConfigPost('/v1/services/status', {}, gateWay, 'GET');
    },

    // 默认服务健康检查
    healthCheck() {
        return aiConfigPost('/v1/health', {}, gateWay, 'GET');
    },

    // DeepSeek 服务健康检查
    deepseekHealth() {
        return aiConfigPost('/v1/services/deepseek/health', {}, gateWay, 'GET');
    },

    // Dify 服务健康检查
    difyHealth() {
        return aiConfigPost('/v1/services/dify/health', {}, gateWay, 'GET');
    },

    // OpenAI/vLLM 服务健康检查
    openaiHealth() {
        return aiConfigPost('/v1/services/openai/health', {}, gateWay, 'GET');
    },

    // ==================== 模型列表 ====================

    // 获取默认服务模型列表
    getModels() {
        return aiConfigPost('/v1/models', {}, gateWay, 'GET');
    },

    // 获取 DeepSeek 模型列表
    getDeepseekModels() {
        return aiConfigPost('/v1/services/deepseek/models', {}, gateWay, 'GET');
    },

    // 获取 Dify 模型列表
    getDifyModels() {
        return aiConfigPost('/v1/services/dify/models', {}, gateWay, 'GET');
    },

    // 获取 OpenAI/vLLM 模型列表
    getOpenaiModels() {
        return aiConfigPost('/v1/services/openai/models', {}, gateWay, 'GET');
    },

    // ==================== 便捷方法 ====================

    // 根据服务类型发送聊天消息
    chatByService(service, params) {
        const serviceMap = {
            'deepseek': this.deepseekChat,
            'dify': this.difyChat,
            'openai': this.openaiChat,
            'default': this.defaultChat
        };

        const chatMethod = serviceMap[service] || serviceMap['default'];
        return chatMethod.call(this, params);
    },

    // 检查指定服务健康状态
    checkServiceHealth(service) {
        const healthMap = {
            'deepseek': this.deepseekHealth,
            'dify': this.difyHealth,
            'openai': this.openaiHealth,
            'default': this.healthCheck
        };

        const healthMethod = healthMap[service] || healthMap['default'];
        return healthMethod.call(this);
    },

    // 获取指定服务的模型列表
    getServiceModels(service) {
        const modelsMap = {
            'deepseek': this.getDeepseekModels,
            'dify': this.getDifyModels,
            'openai': this.getOpenaiModels,
            'default': this.getModels
        };

        const modelsMethod = modelsMap[service] || modelsMap['default'];
        return modelsMethod.call(this);
    }
};
