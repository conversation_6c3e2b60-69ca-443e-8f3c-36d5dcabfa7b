import norMalConfig from '../normal-config';
const gateWay = '/selfanalyticsSevice';

export default {
    // 统计租户调用数据
    getTenantStats(params) {
        return norMalConfig('/statistics/getTenantStats', params, gateWay);
    },
    // 获取调用排行
    getCallRanking(params) {
        return norMalConfig('/statistics/getCallRanking', params, gateWay);
    },
    // 获取租户调用情况
    getTenantCallInfo(params) {
        return norMalConfig('/statistics/getTenantCallInfo', params, gateWay);
    },
    // 获取MCP资产情况
    getMCPAssetInfo(params) {
        return norMalConfig('/statistics/getAssetInfo', params, gateWay);
    }
};
