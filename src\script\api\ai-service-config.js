import axios from 'axios';
const token = localStorage.getItem('token');
// import { reqUtil } from 'mtex-rams-core';
// const { decodeResult, tryEncodeParam } = reqUtil;

// const encryption = !true;

// post 请求
const aiConfigPost = function ({ url, params = {}, headers = {}, gateWay = '' }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .post(gateWay + url, newParams, {
                headers: {
                    token: token
                }
            })
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
                return;
            })
            .catch((err) => {
                reject({
                    success: 111,
                    errorMessage: err.message
                });
            });
    });
};
// get 请求
export const aiConfigGet = function ({ url, params = {}, headers = {}, gateWay = '' }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .get(gateWay + url, newParams, {
                headers: {
                    token: token
                }
            })
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
            })
            .catch((err) => {
                reject('');
            });
    });
};
export default aiConfigPost;
