// 模型映射测试文件
// 用于验证模型选择与API接口的映射关系

/**
 * 测试模型映射逻辑
 */
export function testModelMapping() {
    // 模拟 HomePage.vue 中的映射方法
    function getServiceByModel(model) {
        const serviceMap = {
            'DeepSeek': 'deepseek',
            'Dify': 'dify',
            'OpenAI/vLLM': 'openai'
        };
        return serviceMap[model] || 'deepseek';
    }

    function getModelName(model) {
        const modelMap = {
            'DeepSeek': 'deepseek-chat',
            'Dify': 'dify-chatflow',
            'OpenAI/vLLM': 'qwen3-30b-a3b'
        };
        return modelMap[model] || 'deepseek-chat';
    }

    // 测试用例
    const testCases = [
        {
            userSelection: 'DeepSeek',
            expectedService: 'deepseek',
            expectedModelName: 'deepseek-chat',
            expectedApiMethod: 'defaultChat',
            description: '无接入知识库，可简单测试问答'
        },
        {
            userSelection: 'Dify',
            expectedService: 'dify',
            expectedModelName: 'dify-chatflow',
            expectedApiMethod: 'difyChat',
            description: '接入数据资产知识库，最终使用该接口问答'
        },
        {
            userSelection: 'OpenAI/vLLM',
            expectedService: 'openai',
            expectedModelName: 'qwen3-30b-a3b',
            expectedApiMethod: 'openaiChat',
            description: '本地部署的vLLM模型服务'
        }
    ];

    console.log('=== 模型映射测试开始 ===');
    
    testCases.forEach((testCase, index) => {
        const actualService = getServiceByModel(testCase.userSelection);
        const actualModelName = getModelName(testCase.userSelection);
        
        console.log(`\n测试用例 ${index + 1}: ${testCase.userSelection}`);
        console.log(`描述: ${testCase.description}`);
        console.log(`期望服务: ${testCase.expectedService}, 实际服务: ${actualService}`);
        console.log(`期望模型: ${testCase.expectedModelName}, 实际模型: ${actualModelName}`);
        console.log(`对应API方法: Assistant.${testCase.expectedApiMethod}`);
        
        const serviceMatch = actualService === testCase.expectedService;
        const modelMatch = actualModelName === testCase.expectedModelName;
        
        if (serviceMatch && modelMatch) {
            console.log('✅ 测试通过');
        } else {
            console.log('❌ 测试失败');
            if (!serviceMatch) console.log(`  服务映射错误: 期望 ${testCase.expectedService}, 实际 ${actualService}`);
            if (!modelMatch) console.log(`  模型映射错误: 期望 ${testCase.expectedModelName}, 实际 ${actualModelName}`);
        }
    });

    console.log('\n=== 模型映射测试结束 ===');
}

/**
 * 模拟API调用流程测试
 */
export function testApiCallFlow() {
    console.log('\n=== API调用流程测试 ===');
    
    const mockMessage = {
        text: "测试消息",
        model: "Dify",
        timestamp: new Date().toISOString()
    };

    // 模拟 handleSendMessage 中的逻辑
    function simulateApiCall(message) {
        function getServiceByModel(model) {
            const serviceMap = {
                'DeepSeek': 'deepseek',
                'Dify': 'dify',
                'OpenAI/vLLM': 'openai'
            };
            return serviceMap[model] || 'deepseek';
        }

        function getModelName(model) {
            const modelMap = {
                'DeepSeek': 'deepseek-chat',
                'Dify': 'dify-chatflow',
                'OpenAI/vLLM': 'qwen3-30b-a3b'
            };
            return modelMap[model] || 'deepseek-chat';
        }

        const service = getServiceByModel(message.model);
        const modelName = getModelName(message.model);
        
        const apiParams = {
            model: modelName,
            messages: [
                { role: "user", content: message.text }
            ],
            max_tokens: 2048,
            temperature: 0.7
        };

        let apiMethod;
        if (service === 'dify') {
            apiParams.parameters = {
                industry: '位置大数据',
                analysis_type: 'general',
                user_id: 'user_001'
            };
            apiMethod = 'Assistant.difyChat';
        } else if (service === 'openai') {
            apiMethod = 'Assistant.openaiChat';
        } else {
            apiMethod = 'Assistant.defaultChat';
        }

        return {
            service,
            modelName,
            apiMethod,
            apiParams
        };
    }

    const result = simulateApiCall(mockMessage);
    
    console.log(`用户选择模型: ${mockMessage.model}`);
    console.log(`映射到服务: ${result.service}`);
    console.log(`实际模型名: ${result.modelName}`);
    console.log(`调用方法: ${result.apiMethod}`);
    console.log('API参数:', JSON.stringify(result.apiParams, null, 2));
    
    console.log('=== API调用流程测试结束 ===');
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    testModelMapping();
    testApiCallFlow();
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.modelMappingTest = {
        testModelMapping,
        testApiCallFlow,
        runAllTests
    };
    console.log('模型映射测试工具已加载，可在控制台中调用：');
    console.log('- window.modelMappingTest.runAllTests() // 运行所有测试');
    console.log('- window.modelMappingTest.testModelMapping() // 测试模型映射');
    console.log('- window.modelMappingTest.testApiCallFlow() // 测试API调用流程');
}
