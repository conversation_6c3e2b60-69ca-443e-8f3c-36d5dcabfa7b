<template>
    <div class="home-page">
        <!-- 侧边栏组件 -->
        <Sidebar
            :menu-groups="menuGroups"
            :active-menu-item="activeMenuItem"
            @collapse-change="handleCollapseChange"
            @new-chat="handleNewChat"
            @search="handleSearch"
            @search-select="handleSearchSelect"
            @menu-item-click="handleMenuItemClick"
        />

        <!-- 主内容区域组件 -->
        <MainContent
            v-if="currentView === 'home'"
            :welcome-config="welcomeConfig"
            :input-config="inputConfig"
            :feature-cards="featureCards"
            :model-options="modelOptions"
            @card-click="handleCardClick"
            @send-message="handleSendMessage"
        />

        <!-- 聊天页面组件（预留） -->
        <ChatPage
            v-if="currentView === 'chat'"
            :messages="chatMessages"
            :model-options="modelOptions"
            @send-message="handleSendMessage"
        />
    </div>
</template>

<script>
import Sidebar from '@/script/components/Sidebar.vue';
import MainContent from '@/script/components/MainContent.vue';
import ChatPage from '@/script/components/ChatPage.vue';
import Assistant from '@/script/api/module/assistant.js';

export default {
    name: 'HomePage',
    components: {
        Sidebar,
        MainContent,
        ChatPage
    },
    data() {
        return {
            currentView: 'home', // 'home' 或 'chat'
            activeMenuItem: null,
            chatMessages: [], // 聊天消息列表

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            },

            inputConfig: {
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            },

            modelOptions: [
                { label: 'DeepSeek V3', value: 'deepseek-v3', desc: '适合大部分任务' },
                { label: 'GPT-4', value: 'gpt-4', desc: '适合多种应用场景' },
                { label: 'Claude-3', value: 'claude-3', desc: '适合多种应用场景' }
            ],

            menuGroups: [
                {
                    key: 'today',
                    title: '今天',
                    items: [
                        { id: 1, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 2, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 3, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 4, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'yesterday',
                    title: '昨天',
                    items: [
                        { id: 6, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 7, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 8, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 9, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'week',
                    title: '一周内',
                    items: [
                        { id: 10, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 11, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month',
                    title: '30天内',
                    items: [
                        { id: 12, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 13, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 14, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 15, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month-3',
                    title: '90天内',
                    items: [
                        { id: 16, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 17, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 18, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 19, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                }
            ],

            featureCards: [
                {
                    id: 'location-capability',
                    bg: require('@/img/main/card-bg-1.png'),
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据用户需求及业务口径，结合开放目录、区域洞察AP1、实时事件、平台指标，将匹配的能力以不同的组件形式向用户展示。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    bg: require('@/img/main/card-bg-2.png'),
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订购向导',
                    description:
                        '分析用户需求，引导用户订购推荐的数据资产(API/数据开放目录表等)，同时输出可视化操作步骤指引用户订购。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    bg: require('@/img/main/card-bg-3.png'),
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求规格',
                    description:
                        '提取用户需求中的时空信息，包括业务背景、业务口径、数据账期地理范围，以格式化方式呈现，支持生成并下载需求规格说明书。',
                    action: 'business-data'
                }
            ]
        };
    },

    methods: {
        // 处理侧边栏收起状态变化
        handleCollapseChange(isCollapse) {
            console.log('侧边栏收起状态变化:', isCollapse);
        },

        // 处理新对话
        handleNewChat() {
            console.log('新对话');
            this.currentView = 'home';
            this.chatMessages = [];
            this.activeMenuItem = null;
        },

        // 处理搜索输入
        handleSearch(value) {
            console.log('搜索:', value);
        },

        // 处理搜索选择
        handleSearchSelect(item) {
            console.log('选择搜索项:', item);
            this.activeMenuItem = item.id;
            // 可以在这里切换到聊天页面并加载对应的对话
            this.$emit('search-select', item);
        },

        // 处理菜单项点击
        handleMenuItemClick(item) {
            console.log('点击菜单项:', item);
            this.activeMenuItem = item.id;
            // 可以在这里切换到聊天页面并加载对应的对话
            this.$emit('menu-item-click', item);
        },

        // 处理功能卡片点击
        handleCardClick(card) {
            console.log('点击功能卡片:', card);
            this.$emit('card-click', card);

            // 根据卡片类型设置预设问题并切换到聊天页面
            const presetQuestions = {
                'location-capability': '请为我推荐位置能力使用方案',
                'asset-subscription': '我想了解位置资产订阅相关问题',
                'business-requirements': '请帮我分析位置业务需求数据'
            };

            if (presetQuestions[card.id]) {
                // 创建用户消息
                const userMessage = {
                    text: presetQuestions[card.id],
                    type: 'user',
                    timestamp: new Date().toISOString()
                };

                this.chatMessages = [userMessage];
                this.currentView = 'chat';
            }
        },

        // 处理发送消息
        async handleSendMessage(message) {
            console.log('发送消息:', message);

            // 添加用户消息到聊天记录
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.chatMessages.push(userMessage);

            // 切换到聊天页面
            this.currentView = 'chat';

            // 调用 AI API 获取回复
            try {
                // 构建聊天历史
                const messages = this.chatMessages
                    .filter((msg) => msg.type === 'user' || msg.type === 'assistant')
                    .map((msg) => ({
                        role: msg.type === 'user' ? 'user' : 'assistant',
                        content: msg.text
                    }));

                // 根据选择的模型调用对应的服务
                const apiParams = {
                    model: this.getModelName(message.model),
                    messages: messages,
                    max_tokens: 2048,
                    temperature: 0.7
                };

                let response;
                const service = this.getServiceByModel(message.model);

                if (service === 'dify') {
                    // Dify 服务可能需要额外参数
                    apiParams.parameters = {
                        industry: '位置大数据',
                        analysis_type: 'general',
                        user_id: 'user_001'
                    };
                    response = await Assistant.difyChat(apiParams);
                } else if (service === 'openai') {
                    response = await Assistant.openaiChat(apiParams);
                } else {
                    // 默认使用 DeepSeek
                    response = await Assistant.defaultChat(apiParams);
                }

                // 处理 API 响应
                if (response && response.choices && response.choices.length > 0) {
                    const aiResponse = {
                        text: response.choices[0].message.content,
                        type: 'assistant',
                        timestamp: new Date().toISOString(),
                        model: message.model
                    };
                    this.chatMessages.push(aiResponse);
                } else {
                    throw new Error('API 响应格式错误');
                }
            } catch (error) {
                console.error('API 调用失败:', error);

                // 添加错误消息
                const errorResponse = {
                    text: `抱歉，服务暂时不可用。错误信息：${error.message || '未知错误'}`,
                    type: 'assistant',
                    timestamp: new Date().toISOString(),
                    isError: true
                };
                this.chatMessages.push(errorResponse);
            }

            this.$emit('send-message', message);
        },

        // 根据模型获取对应的服务
        getServiceByModel(model) {
            const serviceMap = {
                'deepseek-v3': 'deepseek',
                'gpt-4': 'openai',
                'claude-3': 'openai',
                'dify-chatflow': 'dify'
            };
            return serviceMap[model] || 'deepseek';
        },

        // 获取实际的模型名称
        getModelName(model) {
            const modelMap = {
                'deepseek-v3': 'deepseek-chat',
                'gpt-4': 'qwen3-30b-a3b',
                'claude-3': 'qwen3-30b-a3b',
                'dify-chatflow': 'dify-chatflow'
            };
            return modelMap[model] || 'deepseek-chat';
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100vh;
}
</style>
