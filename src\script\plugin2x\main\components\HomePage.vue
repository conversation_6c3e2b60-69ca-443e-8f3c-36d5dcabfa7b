<template>
    <div class="home-page">
        <!-- 侧边栏 -->
        <div class="sidebar" :style="{ width: isCollapse ? '3.75rem' : '18.75rem' }">
            <!-- 展开状态 -->
            <div
                class="sidebar-open"
                :style="{ width: isCollapse ? '0' : '100%', height: isCollapse ? '0' : '100%' }"
            >
                <div class="sidebar-header">
                    <div class="logo-section">
                        <img src="~@/img/main/logo.png" alt="logo" class="logo" />
                        <img src="~@/img/main/main-title.png" alt="logo-title" class="title" />
                        <div class="collapse-btn" @click="handleCollapse(true)"></div>
                    </div>
                    <div class="new-chat">
                        <div class="new-chat-icon"></div>
                        <span class="new-chat-text">新对话</span>
                    </div>
                </div>
                <div class="search-section">
                    <el-autocomplete
                        v-model="searchText"
                        placeholder="搜索"
                        prefix-icon="el-icon-search"
                        clearable
                        class="search-input"
                        popper-class="search-autocomplete-popper"
                        :fetch-suggestions="querySearchAsync"
                        :trigger-on-focus="true"
                        @input="handleSearch"
                        @select="handleSearchSelect"
                    >
                        <template slot-scope="{ item }">
                            <div class="search-suggestion-item">
                                <div class="search-item-text">{{ item.label }}</div>
                            </div>
                        </template>
                    </el-autocomplete>
                </div>
                <div class="menu-section custom-scrollbar">
                    <template v-for="menuGroup in filteredMenuGroups">
                        <div class="menu-item" :key="`title-${menuGroup.key}`">
                            <span class="menu-title">{{ menuGroup.title }}</span>
                        </div>
                        <div class="menu-group" :key="`group-${menuGroup.key}`">
                            <div
                                class="menu-group-item"
                                :class="{ active: activeMenuItem === item.id }"
                                v-for="item in menuGroup.items"
                                :key="item.id"
                                @click="handleMenuItemClick(item)"
                            >
                                <span class="menu-group-text">{{ item.text }}</span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <!-- 收起状态 -->
            <div
                class="sidebar-close"
                :style="{ width: isCollapse ? '100%' : '0', height: isCollapse ? '100%' : '0' }"
            >
                <img src="~@/img/main/logo.png" alt="logo" class="logo" />
                <div class="collapse-btn" @click="handleCollapse(false)"></div>
                <el-tooltip
                    class="item"
                    effect="dark"
                    content="新对话"
                    placement="right"
                    popper-class="new-chat-popper"
                >
                    <div class="new-chat">
                        <div class="new-chat-icon"></div>
                    </div>
                </el-tooltip>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <div class="welcome-index">
                    <!-- 欢迎区域 -->
                    <div class="welcome-section">
                        <div class="welcome-icon">
                            <img src="~@/img/main/logo-big.png" alt="logo" class="logo-main" />
                        </div>
                        <h1 class="welcome-title">{{ welcomeConfig.title }}</h1>
                        <p class="welcome-desc">{{ welcomeConfig.description }}</p>
                    </div>

                    <!-- 功能卡片区域 -->
                    <div class="feature-cards">
                        <el-card
                            class="feature-card"
                            :style="{ '--card-bg': `url(${card.bg})` }"
                            v-for="(card, index) in featureCards"
                            :key="index"
                            shadow="never"
                            :body-style="{ padding: 0, height: '100%' }"
                            @click.native="handleCardClick(card)"
                        >
                            <div class="card-content">
                                <div class="card-icon">
                                    <img :src="card.icon" :alt="card.title" />
                                </div>
                                <h3 class="card-title">{{ card.title }}</h3>
                                <p class="card-desc text-ellipsis">{{ card.description }}</p>
                            </div>
                        </el-card>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-section">
                    <div class="input-container">
                        <textarea
                            id="chat-input"
                            class="chat-input custom-scrollbar"
                            v-model="inputText"
                            :placeholder="inputConfig.placeholder"
                            resize="none"
                            rows="2"
                            @keydown.native="handleKeydown"
                        ></textarea>
                        <div class="input-actions">
                            <el-select
                                v-model="selectedModel"
                                size="small"
                                class="model-select"
                                popper-class="dataUsageAssistant-theme model-select-popper"
                            >
                                <el-option
                                    v-for="model in modelOptions"
                                    :key="model.value"
                                    :label="model.label"
                                    :value="model.value"
                                >
                                    <div class="model-option">
                                        <div class="model-option-text">
                                            <div class="model-name">{{ model.label }}</div>
                                            <div class="model-desc">{{ model.desc }}</div>
                                        </div>
                                        <i
                                            class="el-icon-check model-selected"
                                            v-if="selectedModel === model.value"
                                        ></i>
                                    </div>
                                </el-option>
                            </el-select>
                            <div class="right-actions">
                                <el-tooltip
                                    class="item"
                                    effect="dark"
                                    content="语音输入"
                                    placement="top"
                                    popper-class="mic-popper"
                                >
                                    <div class="mic-btn">
                                        <i class="el-icon-microphone"></i>
                                    </div>
                                </el-tooltip>
                                <el-tooltip
                                    class="item"
                                    effect="dark"
                                    content="请输入你的问题"
                                    placement="top"
                                    popper-class="mic-popper"
                                    :disabled="!!inputText.trim()"
                                >
                                    <div
                                        class="send-btn"
                                        :class="{ 'is-disabled': !inputText.trim() }"
                                        @click="sendMessage"
                                    >
                                        <i class="el-icon-top"></i>
                                    </div>
                                </el-tooltip>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HomePage',
    data() {
        return {
            isCollapse: false,
            searchText: '',
            inputText: '',
            selectedModel: 'deepseek-v3',
            filteredMenuItems: {},

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            },

            inputConfig: {
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            },

            modelOptions: [
                { label: 'DeepSeek V3', value: 'deepseek-v3', desc: '适合大部分任务' },
                { label: 'GPT-4', value: 'gpt-4', desc: '适合多种应用场景' },
                { label: 'Claude-3', value: 'claude-3', desc: '适合多种应用场景' }
            ],

            activeMenuItem: null,
            menuGroups: [
                {
                    key: 'today',
                    title: '今天',
                    items: [
                        { id: 1, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 2, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 3, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 4, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'yesterday',
                    title: '昨天',
                    items: [
                        { id: 6, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 7, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 8, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 9, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'week',
                    title: '一周内',
                    items: [
                        { id: 10, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 11, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month',
                    title: '30天内',
                    items: [
                        { id: 12, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 13, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 14, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 15, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month-3',
                    title: '90天内',
                    items: [
                        { id: 16, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 17, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 18, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 19, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                }
            ],

            featureCards: [
                {
                    id: 'location-capability',
                    bg: require('@/img/main/card-bg-1.png'),
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据用户需求及业务口径，结合开放目录、区域洞察AP1、实时事件、平台指标，将匹配的能力以不同的组件形式向用户展示。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    bg: require('@/img/main/card-bg-2.png'),
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订购向导',
                    description:
                        '分析用户需求，引导用户订购推荐的数据资产(API/数据开放目录表等)，同时输出可视化操作步骤指引用户订购。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    bg: require('@/img/main/card-bg-3.png'),
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求规格',
                    description:
                        '提取用户需求中的时空信息，包括业务背景、业务口径、数据账期地理范围，以格式化方式呈现，支持生成并下载需求规格说明书。',
                    action: 'business-data'
                }
            ]
        };
    },

    computed: {
        // 根据搜索文本过滤菜单项
        filteredMenuGroups() {
            if (!this.searchText.trim()) {
                return this.menuGroups;
            }

            return this.menuGroups
                .map((group) => ({
                    ...group,
                    items: group.items.filter((item) =>
                        item.text.toLowerCase().includes(this.searchText.toLowerCase())
                    )
                }))
                .filter((group) => group.items.length > 0);
        },

        // 获取所有搜索数据源
        allSearchItems() {
            const allItems = [];

            // 合并所有菜单组的数据
            this.menuGroups.forEach((group) => {
                group.items.forEach((item) => {
                    allItems.push({
                        value: item.text,
                        label: item.text,
                        id: item.id,
                        type: item.type,
                        groupKey: group.key,
                        groupTitle: group.title,
                        timestamp: item.timestamp
                    });
                });
            });

            return allItems;
        }
    },

    methods: {
        // 处理侧边栏收起
        handleCollapse(val) {
            console.log('handleCollapse', val);

            this.isCollapse = val;
        },

        // 处理搜索输入
        handleSearch(value) {
            console.log('搜索:', value);
        },

        // 自动完成查询方法
        querySearchAsync(queryString, callback) {
            if (!queryString || queryString.trim() === '') {
                callback([]);
                return;
            }

            const results = this.allSearchItems.filter((item) => {
                return item.value.toLowerCase().includes(queryString.toLowerCase());
            });

            // 限制结果数量，避免列表过长
            const limitedResults = results.slice(0, 10);

            // 为每个结果添加显示格式
            const formattedResults = limitedResults.map((item) => ({
                ...item,
                value: item.value,
                label: item.value,
                groupInfo: item.groupTitle
            }));

            callback(formattedResults);
        },

        // 处理自动完成选择
        handleSearchSelect(item) {
            console.log('选择搜索项:', item);

            // 设置搜索文本为选中项的值
            this.searchText = item.value;

            // 触发菜单项点击事件
            const menuItem = {
                id: item.id,
                text: item.value,
                type: item.type,
                timestamp: item.timestamp
            };

            this.handleMenuItemClick(menuItem);

            // 发出选择事件
            this.$emit('search-select', item);
        },

        // 获取过滤后的菜单项
        getFilteredItems(items) {
            if (!this.searchText.trim()) {
                return items;
            }
            return items.filter((item) =>
                item.text.toLowerCase().includes(this.searchText.toLowerCase())
            );
        },

        // 处理菜单项点击
        handleMenuItemClick(item) {
            console.log('点击菜单项:', item);
            this.activeMenuItem = item.id;
            this.$emit('menu-item-click', item);
        },

        // 处理功能卡片点击
        handleCardClick(card) {
            console.log('点击功能卡片:', card);
            this.$emit('card-click', card);

            // 根据卡片类型设置预设问题
            const presetQuestions = {
                'location-capability': '请为我推荐位置能力使用方案',
                'asset-subscription': '我想了解位置资产订阅相关问题',
                'business-requirements': '请帮我分析位置业务需求数据'
            };

            if (presetQuestions[card.id]) {
                this.inputText = presetQuestions[card.id];
            }
        },

        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim()) {
                const message = {
                    text: this.inputText.trim(),
                    model: this.selectedModel,
                    timestamp: new Date().toISOString()
                };

                console.log('发送消息:', message);
                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
}

// 侧边栏样式
.sidebar {
    width: 18.75rem;
    background: rgba(255, 255, 255, 0.8);
    height: 100vh;
    transition: all 0.3s ease;

    .sidebar-open {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: width 0.3s ease;
        .sidebar-header {
            padding: 1.75rem 0.75rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            .logo-section {
                display: flex;
                align-items: center;
                gap: 0.625rem;

                .logo {
                    width: 2.25rem;
                    height: 2.25rem;
                }

                .title {
                    width: 10rem;
                    height: 1.5rem;
                }

                .collapse-btn {
                    margin-left: auto;
                    width: 1.5rem;
                    height: 1.5rem;
                    background-image: url('~@/img/main/sidebar-collapse.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    cursor: pointer;
                }
            }
            .new-chat {
                height: 2.5rem;
                background: rgba(2, 101, 254, 0.05);
                border-radius: 0.5rem;
                border: 0.0625rem solid rgba(2, 101, 254, 0.5);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0 0.75rem;
                cursor: pointer;
                user-select: none;
                transition: all 0.2 ease-in-out;
                &-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                    background-image: url('~@/img/main/new-chat.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                }
                &-text {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #0265fe;
                    line-height: 1.25rem;
                }
                &:hover {
                    background: rgba(2, 101, 254, 0.1);
                }
            }
        }

        .search-section {
            padding: 1.5rem 0.75rem 1.875rem;

            /deep/.el-autocomplete.search-input {
                width: 100%;

                .el-input__inner {
                    height: 2.25rem;
                    background: #ffffff;
                    border-radius: 0.5rem;
                    border: 0.0625rem solid rgba(213, 214, 216, 0.5);
                }
            }
        }

        .menu-section {
            min-height: 0;
            flex: 1;
            padding: 0 0.75rem 1.5rem;
            overflow-y: auto;
            --thumb-color: rgba(0, 0, 0, 0.1);

            .menu-item {
                padding: 0 0.75rem 0.5rem;
                .menu-title {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #666666;
                    line-height: 1.25rem;
                }
            }

            .menu-group {
                margin-bottom: 1.875rem;

                .menu-group-item {
                    padding: 0.5rem 0.75rem;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                    border-radius: 0.5rem;
                    &:hover {
                        background: rgb(239 246 255);
                    }
                    &.active {
                        background: rgb(219 234 254);
                        &:hover {
                            background: rgb(219 234 254);
                        }
                    }
                }
                .menu-group-text {
                    font-size: 0.875rem;
                    color: #374151;
                    display: block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
    .sidebar-close {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 1.75rem;
        overflow: hidden;
        transition: width 0.3s ease;
        .logo {
            width: 2.25rem;
            height: 2.25rem;
            margin-bottom: 1.25rem;
        }
        .collapse-btn {
            width: 1.5rem;
            height: 1.5rem;
            background-image: url('~@/img/main/sidebar-collapse.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
            margin-bottom: 0.875rem;
        }
        .new-chat {
            width: 2.25rem;
            height: 2.25rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            &:hover {
                background: rgba(2, 101, 254, 0.1);
            }
            &-icon {
                width: 1.25rem;
                height: 1.25rem;
                background-image: url('~@/img/main/new-chat-gray.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
    }
}

// 主内容区域样式
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;

        .welcome-index {
            min-height: 0;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            gap: 3.75rem;

            .welcome-section {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;

                .welcome-icon {
                    width: 6.25rem;
                    height: 6.25rem;
                    margin-bottom: 1.875rem;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .welcome-title {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 500;
                    font-size: 2rem;
                    color: #001024;
                    line-height: 2.5rem;
                    margin: 0 0 1.5rem 0;
                }

                .welcome-desc {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #666666;
                    line-height: 1.5rem;
                    margin-bottom: 0;
                }
            }

            .feature-cards {
                display: flex;
                gap: 1.5rem;

                .feature-card {
                    --card-bg: url('~@/img/main/card-bg-1.png');
                    width: 19rem;
                    height: 15.4375rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background-color: transparent;
                    background-image: var(--card-bg);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    border: none;
                    &:hover {
                        transform: translateY(-0.125rem);
                    }

                    .card-content {
                        width: 100%;
                        height: 100%;
                        padding: 1.5rem;

                        .card-icon {
                            margin-bottom: 1.125rem;

                            img {
                                width: 3rem;
                                height: 3rem;
                            }
                        }

                        .card-title {
                            font-weight: 500;
                            font-size: 1.25rem;
                            color: #222222;
                            line-height: 1.75rem;
                            margin: 0 0 0.75rem 0;
                        }

                        .card-desc {
                            --line-clamp: 4;
                            font-weight: 400;
                            font-size: 0.875rem;
                            color: #666666;
                            line-height: 1.25rem;
                            margin: 0;
                        }
                    }
                }
            }
        }

        .input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;

            .input-container {
                width: 60rem;
                height: 8.75rem;
                display: flex;
                flex-direction: column;
                background: rgba(255, 255, 255, 0.92);
                border-radius: 0.75rem;
                border: 0.0625rem solid #1765ff;
                padding: 0.75rem;
                gap: 0.75rem;

                .chat-input {
                    min-height: 0;
                    flex: 1;
                    resize: none;
                    border: none;
                    outline: none;
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #222222;
                    line-height: 1.375rem;
                    &::placeholder {
                        font-family: HONORSansCN, HONORSansCN;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #999999;
                    }
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .model-select {
                        width: 9.125rem;
                        height: 32px;
                        /deep/ .el-input__inner {
                            background: transparent;
                            border-radius: 0.5rem;
                            border: 0.0625rem solid #e0e0e0;
                            font-weight: 500;
                            font-size: 0.875rem;
                            color: #222222;
                        }
                    }
                    .right-actions {
                        margin-left: auto;
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                        .mic-btn {
                            width: 2rem;
                            height: 2rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: transparent;
                            border-radius: 0.375rem;
                            cursor: pointer;

                            .el-icon-microphone {
                                font-size: 1.25rem;
                                color: #0c1827;
                            }
                            &:hover {
                                background: rgba(2, 101, 254, 0.1);
                            }
                        }
                        .send-btn {
                            width: 2rem;
                            height: 2rem;
                            border-radius: 50%;
                            padding: 0;
                            background: #87b3fe;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            .el-icon-top {
                                color: #fff;
                                font-weight: 500;
                                font-size: 1rem;
                            }

                            &.is-disabled {
                                opacity: 0.5;
                                cursor: not-allowed;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
<style lang="less">
.search-autocomplete-popper {
    max-height: 18.75rem;
    overflow-y: auto;
    background: #ffffff;
    box-shadow: 0 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0 0.375rem 1rem 0 rgba(0, 0, 0, 0.08), 0 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.5rem;
}
.model-select-popper {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow: 0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    .el-select-dropdown__item {
        height: fit-content;
        line-height: normal;
        padding: 0;
        &.hover {
            background: transparent;
        }
        &:hover {
            background: #f2f2f2;
        }
        &.selected {
            background: #f2f2f2;
        }
    }
    .el-select-dropdown__list {
        padding: 0;
    }
    .model-option {
        width: 10.75rem;
        height: 3.5rem;
        border-radius: 0.5rem;
        display: grid;
        grid-template-columns: 1fr 1.25rem;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem;
        .model-option-text {
            display: flex;
            flex-direction: column;
            height: fit-content;
            flex: 1;
            .model-name {
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                line-height: 1.25rem;
            }
            .model-desc {
                font-weight: 400;
                font-size: 0.75rem;
                color: #666666;
                line-height: 1.125rem;
            }
        }
        .model-selected {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1rem;
            color: #222222;
        }
    }
}
.mic-popper {
    &.el-tooltip__popper[x-placement^='top'] .popper__arrow {
        bottom: -5px;
    }
}
</style>
