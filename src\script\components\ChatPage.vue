<template>
    <div class="chat-page">
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages" ref="messagesContainer">
                <div 
                    v-for="(message, index) in messages" 
                    :key="index"
                    class="message-item"
                    :class="{ 'user-message': message.type === 'user', 'assistant-message': message.type === 'assistant' }"
                >
                    <div class="message-content">
                        <div class="message-text">{{ message.text }}</div>
                        <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                    </div>
                </div>
                
                <!-- 空状态提示 -->
                <div v-if="messages.length === 0" class="empty-state">
                    <div class="empty-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" />
                    </div>
                    <p class="empty-text">开始您的对话吧</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-section">
                <div class="input-container">
                    <textarea
                        v-model="inputText"
                        :placeholder="placeholder"
                        class="chat-input"
                        rows="2"
                        @keydown="handleKeydown"
                    ></textarea>
                    <div class="input-actions">
                        <el-select
                            v-model="selectedModel"
                            size="small"
                            class="model-select"
                        >
                            <el-option
                                v-for="model in modelOptions"
                                :key="model.value"
                                :label="model.label"
                                :value="model.value"
                            >
                            </el-option>
                        </el-select>
                        <div class="send-btn" 
                             :class="{ 'is-disabled': !inputText.trim() }"
                             @click="sendMessage">
                            <i class="el-icon-top"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ChatPage',
    props: {
        messages: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => [
                { label: 'DeepSeek V3', value: 'deepseek-v3' },
                { label: 'GPT-4', value: 'gpt-4' },
                { label: 'Claude-3', value: 'claude-3' }
            ]
        },
        placeholder: {
            type: String,
            default: '请输入您的问题...'
        }
    },
    data() {
        return {
            inputText: '',
            selectedModel: 'deepseek-v3'
        };
    },
    methods: {
        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim()) {
                const message = {
                    text: this.inputText.trim(),
                    model: this.selectedModel,
                    timestamp: new Date().toISOString(),
                    type: 'user'
                };

                this.$emit('send-message', message);
                this.inputText = '';
            }
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        }
    },
    watch: {
        messages: {
            handler() {
                this.scrollToBottom();
            },
            deep: true
        }
    }
};
</script>

<style scoped lang="less">
.chat-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f5f5f5;

    .chat-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-width: 60rem;
        margin: 0 auto;
        width: 100%;
        padding: 1rem;

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message-item {
                display: flex;
                
                &.user-message {
                    justify-content: flex-end;
                    
                    .message-content {
                        background: #0265fe;
                        color: white;
                        max-width: 70%;
                    }
                }
                
                &.assistant-message {
                    justify-content: flex-start;
                    
                    .message-content {
                        background: white;
                        color: #333;
                        max-width: 70%;
                        border: 1px solid #e0e0e0;
                    }
                }

                .message-content {
                    padding: 0.75rem 1rem;
                    border-radius: 0.75rem;
                    
                    .message-text {
                        font-size: 0.875rem;
                        line-height: 1.5;
                        margin-bottom: 0.25rem;
                    }
                    
                    .message-time {
                        font-size: 0.75rem;
                        opacity: 0.7;
                    }
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                
                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    margin-bottom: 1rem;
                    
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
                
                .empty-text {
                    color: #999;
                    font-size: 0.875rem;
                }
            }
        }

        .chat-input-section {
            margin-top: auto;
            
            .input-container {
                background: white;
                border-radius: 0.75rem;
                border: 1px solid #e0e0e0;
                padding: 0.75rem;
                display: flex;
                flex-direction: column;
                gap: 0.75rem;

                .chat-input {
                    border: none;
                    outline: none;
                    resize: none;
                    font-size: 0.875rem;
                    line-height: 1.5;
                    
                    &::placeholder {
                        color: #999;
                    }
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .model-select {
                        width: 8rem;
                    }

                    .send-btn {
                        margin-left: auto;
                        width: 2rem;
                        height: 2rem;
                        border-radius: 50%;
                        background: #0265fe;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        
                        .el-icon-top {
                            color: white;
                            font-size: 1rem;
                        }

                        &.is-disabled {
                            opacity: 0.5;
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }
    }
}
</style>
