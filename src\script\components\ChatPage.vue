<template>
    <div class="chat-page">
        <div class="chat-title">{{ title }}</div>
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages" ref="messagesContainer">
                <div
                    v-for="(message, index) in messages"
                    :key="index"
                    class="message-item"
                    :class="{
                        'user-message': message.type === 'user',
                        'assistant-message': message.type === 'assistant',
                        'error-message': message.isError
                    }"
                >
                    <div class="message-content">
                        <div class="message-text">{{ message.text }}</div>
                        <!-- <div class="message-time">{{ formatTime(message.timestamp) }}</div> -->
                    </div>
                </div>

                <!-- 空状态提示 -->
                <div v-if="messages.length === 0" class="empty-state">
                    <div class="empty-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" />
                    </div>
                    <p class="empty-text">开始您的对话吧</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-section">
                <MessageInput
                    :placeholder="placeholder"
                    :model-options="modelOptions"
                    :defaultModel="defaultModel"
                    :show-mic-button="true"
                    :show-model-details="true"
                    popper-class="dataUsageAssistant-theme model-select-popper"
                    @send-message="handleSendMessage"
                    @mic-click="handleMicClick"
                />
            </div>
        </div>
    </div>
</template>

<script>
import MessageInput from './MessageInput.vue';

export default {
    name: 'ChatPage',
    components: {
        MessageInput
    },
    props: {
        // 聊天标题
        title: {
            type: String,
            default: '新对话'
        },
        // 聊天消息列表
        messages: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入您的问题...'
        }
    },
    methods: {
        // 处理发送消息
        handleSendMessage(message) {
            // 添加用户类型标识
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.$emit('send-message', userMessage);
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        }
    },
    watch: {
        messages: {
            handler() {
                this.scrollToBottom();
            },
            deep: true
        }
    }
};
</script>

<style scoped lang="less">
.chat-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;

    .chat-title {
        width: 100%;
        height: 3rem;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 22px;
    }

    .chat-container {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin: 0 auto;
        width: 100%;

        .chat-messages {
            width: 60rem;
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message-item {
                display: flex;

                &.user-message {
                    justify-content: flex-end;

                    .message-content {
                        background: #cde1ff;
                        border-radius: 0.75rem 0.25rem 0.75rem 0.75rem;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.assistant-message {
                    justify-content: flex-start;

                    .message-content {
                        background: transparent;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.error-message {
                    justify-content: flex-start;

                    .message-content {
                        background: #fef2f2;
                        border: 1px solid #fecaca;
                        color: #dc2626;
                        max-width: 70%;
                    }
                }

                .message-content {
                    padding: 0.75rem 1rem;
                    border-radius: 0.75rem;

                    .message-text {
                        font-size: 0.875rem;
                        line-height: 1.5;
                    }

                    .message-time {
                        margin-top: 0.25rem;
                        font-size: 0.75rem;
                        opacity: 0.7;
                    }
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    margin-bottom: 1rem;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .empty-text {
                    color: #999;
                    font-size: 0.875rem;
                }
            }
        }

        .chat-input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;
        }
    }
}
</style>
