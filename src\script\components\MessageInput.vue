<template>
    <div class="message-input">
        <div class="input-container">
            <textarea
                class="chat-input custom-scrollbar"
                v-model="inputText"
                :placeholder="placeholder"
                resize="none"
                rows="2"
                @keydown="handleKeydown"
            ></textarea>
            <div class="input-actions">
                <el-select
                    v-model="selectedModel"
                    size="small"
                    class="model-select"
                    :popper-class="popperClass"
                >
                    <el-option
                        v-for="model in modelOptions"
                        :key="model.value"
                        :label="model.label"
                        :value="model.value"
                    >
                        <div class="model-option" v-if="showModelDetails">
                            <div class="model-option-text">
                                <div class="model-name">{{ model.label }}</div>
                                <div class="model-desc">{{ model.desc }}</div>
                            </div>
                            <i
                                class="el-icon-check model-selected"
                                v-if="selectedModel === model.value"
                            ></i>
                        </div>
                        <span v-else>{{ model.label }}</span>
                    </el-option>
                </el-select>
                <div class="right-actions">
                    <!-- 语音输入按钮（可选） -->
                    <el-tooltip
                        v-if="showMicButton"
                        class="item"
                        effect="dark"
                        content="语音输入"
                        placement="top"
                        popper-class="mic-popper"
                    >
                        <div class="mic-btn" @click="handleMicClick">
                            <i class="el-icon-microphone"></i>
                        </div>
                    </el-tooltip>
                    <!-- 发送按钮 -->
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="sendButtonTooltip"
                        placement="top"
                        popper-class="mic-popper"
                        :disabled="!!inputText.trim()"
                    >
                        <div
                            class="send-btn"
                            :class="{ 'is-disabled': !inputText.trim() }"
                            @click="sendMessage"
                        >
                            <i class="el-icon-top"></i>
                        </div>
                    </el-tooltip>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MessageInput',
    props: {
        // 输入框配置
        placeholder: {
            type: String,
            default: '请输入您的问题，Shift+Enter可换行，Enter发送'
        },
        // 模型选项
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },

        // 功能开关
        showMicButton: {
            type: Boolean,
            default: false
        },
        showModelDetails: {
            type: Boolean,
            default: false
        },

        // 其他配置
        popperClass: {
            type: String,
            default: 'model-select-popper'
        },
        sendButtonTooltip: {
            type: String,
            default: '请输入你的问题'
        }
    },
    data() {
        return {
            inputText: '',
            selectedModel: this.defaultModel
        };
    },
    methods: {
        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                // Enter键发送消息
                event.preventDefault();
                this.sendMessage();
            }
            // Shift+Enter允许换行，不需要特殊处理
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim()) {
                const message = {
                    text: this.inputText.trim(),
                    model: this.selectedModel,
                    timestamp: new Date().toISOString()
                };

                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        },

        // 处理语音输入点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 设置输入文本（外部调用）
        setInputText(text) {
            this.inputText = text;
        },

        // 获取输入文本
        getInputText() {
            return this.inputText;
        },

        // 清空输入框
        clearInput() {
            this.inputText = '';
        },

        // 聚焦输入框
        focus() {
            this.$nextTick(() => {
                const input = this.$el.querySelector('.chat-input');
                if (input) {
                    input.focus();
                }
            });
        }
    }
};
</script>

<style scoped lang="less">
.message-input {
    .input-container {
        display: flex;
        flex-direction: column;
        width: 60rem;
        height: 8.75rem;
        background: rgba(255, 255, 255, 0.92);
        border-radius: 0.75rem;
        border: 0.0625rem solid #1765ff;
        padding: 0.75rem;
        gap: 0.75rem;

        .chat-input {
            min-height: 0;
            flex: 1;
            resize: none;
            border: none;
            outline: none;
            font-family: HONORSansCN, HONORSansCN;
            font-weight: 400;
            color: #222222;
            font-size: 1rem;
            line-height: 1.375rem;

            &::placeholder {
                font-family: HONORSansCN, HONORSansCN;
                font-weight: 400;
                font-size: 1rem;
                color: #999999;
            }
        }

        .input-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .model-select {
                width: 9.125rem;
                height: 32px;

                /deep/ .el-input__inner {
                    background: transparent;
                    border-radius: 0.5rem;
                    border: 0.0625rem solid #e0e0e0;
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #222222;
                }
            }

            .right-actions {
                margin-left: auto;
                display: flex;
                align-items: center;
                gap: 1rem;

                .mic-btn {
                    width: 2rem;
                    height: 2rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: transparent;
                    border-radius: 0.375rem;
                    cursor: pointer;

                    .el-icon-microphone {
                        font-size: 1.25rem;
                        color: #0c1827;
                    }

                    &:hover {
                        background: rgba(2, 101, 254, 0.1);
                    }
                }

                .send-btn {
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    padding: 0;
                    background: #87b3fe;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    .el-icon-top {
                        color: #fff;
                        font-weight: 500;
                        font-size: 1rem;
                    }

                    &.is-disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
.model-select-popper {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow: 0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    .el-select-dropdown__item {
        height: fit-content;
        line-height: normal;
        padding: 0;
        &.hover {
            background: transparent;
        }
        &:hover {
            background: #f2f2f2;
        }
        &.selected {
            background: #f2f2f2;
        }
    }
    .el-select-dropdown__list {
        padding: 0;
    }
    .model-option {
        height: 3.5rem;
        border-radius: 0.5rem;
        display: grid;
        grid-template-columns: 1fr 1.25rem;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem;
        .model-option-text {
            display: flex;
            flex-direction: column;
            height: fit-content;
            flex: 1;
            .model-name {
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                line-height: 1.25rem;
            }
            .model-desc {
                font-weight: 400;
                font-size: 0.75rem;
                color: #666666;
                line-height: 1.125rem;
            }
        }
        .model-selected {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1rem;
            color: #222222;
        }
    }
}
.mic-popper {
    &.el-tooltip__popper[x-placement^='top'] .popper__arrow {
        bottom: -5px;
    }
}
</style>
